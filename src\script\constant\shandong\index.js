import shandong from './shandong.json';

/**
 * 山东省地市
 * @param {boolean} isCode - 是否返回编码
 * @param {string} type - 类型，[all:所有地市,metro:有地铁地市]
 * @returns {Array} 地市列表
 */
export const SHANDONG_CITY = ({ isCode = true, type = 'all' } = {}) => {
    if (!shandong || !shandong.children || !Array.isArray(shandong.children)) {
        return [];
    }

    let cities = shandong.children;

    // 如果类型是metro，只返回有地铁的城市
    if (type === 'metro') {
        // const metroCity = ['370100', '370200'];
        // cities = cities.filter((city) => metroCity.includes(city.districtId.toString()));
    }

    return cities.map((city) => ({
        label: city.districtName,
        value: isCode ? city.districtId.toString() : city.districtName
    }));
};

/**
 * 山东省区县
 * @param {boolean} isCode - 是否返回编码
 * @param {string} cityId - 地市编码 为空则查所有地市的所有区县
 * @returns {Array} 区县列表
 */
export const SHANDONG_DISTRICT = ({ isCode = true, cityId = '' } = {}) => {
    if (!shandong || !shandong.children || !Array.isArray(shandong.children)) {
        return [];
    }

    const districts = [];

    shandong.children.forEach((city) => {
        if (cityId && city.districtId.toString() !== cityId) {
            return;
        }

        if (city.children && Array.isArray(city.children)) {
            city.children.forEach((district) => {
                districts.push({
                    label: district.districtName,
                    value: isCode ? district.districtId.toString() : district.districtName,
                    cityId: city.districtId.toString(),
                    cityName: city.districtName
                });
            });
        }
    });

    return districts;
};
