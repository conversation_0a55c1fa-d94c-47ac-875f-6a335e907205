<template>
    <div class="page-container gdb-dark-theme">
        <Header @search="handleSearch" />
        <div class="main-content">
            <LeftNav :data="navItems" :currentNav.sync="currentNav" @nav-change="handleNavChange" />
            <div class="content-section">
                <ContentPanel :cardList="cardList" @card-click="handleCardClick" />
            </div>
        </div>
        <DetailDialog :visible.sync="dialogVisible" v-if="dialogVisible" :info="currentCardInfo" />
    </div>
</template>

<script>
import remMixin from '@/script/utils/mixin/remMixin.js';

export default {
    name: 'DisplayBoard',
    components: {
        Header: () =>
            import(/* webpackChunkName: "displayBoard-header" */ './components/Header/index.vue'),
        LeftNav: () =>
            import(/* webpackChunkName: "displayBoard-nav" */ './components/LeftNav/index.vue'),
        ContentPanel: () =>
            import(
                /* webpackChunkName: "displayBoard-content" */ './components/ContentPanel/index.vue'
            ),
        DetailDialog: () =>
            import(
                /* webpackChunkName: "displayBoard-detaildailog" */ '@/script/components/DetailDialog/index.vue'
            )
    },
    mixins: [remMixin],
    data() {
        return {
            navItems: [],
            navMapping: {
                城市选网: 'el-icon-location',
                地铁出行: 'el-icon-s-promotion',
                公交出行: 'el-icon-s-flag',
                高速出行: 'el-icon-s-marketing',
                高铁出行: 'el-icon-s-data'
            },
            renderTypeMapping: {
                '地图+时间轴': 'mapTimeAxis',
                '比例图-多维': 'multiScaleDiagram',
                '比例图-单维': 'singleScaleDiagram',
                折线图: 'lineChart',
                '表格-占比呈现': 'tableProportion',
                '表格-数值呈现': 'tableValues',
                表格: 'tableValues',
                '地图+点/线': 'mapPointLine',
                '折线图-对比': 'compareLineChart'
            },
            currentNav: '',
            searchKey: '',
            dialogVisible: false,
            currentCardInfo: {},
            cardList: [],
            allCardList: []
        };
    },
    watch: {
        searchKey: {
            handler(val) {
                if (this.currentNav && this.allCardList) {
                    this.cardList = this.allCardList.filter((item) => {
                        // 存在搜索关键词，则过滤
                        if (val) {
                            return item.cardName.includes(this.searchKey);
                        } else {
                            return true;
                        }
                    });
                }
            },
            immediate: true
        }
    },
    mounted() {
        this.getNavItems();
    },
    methods: {
        async getNavItems() {
            let { data } = await $request('post', 'mtexapi/region-service/homepage/sidebar');
            this.navItems = data.map((item) => ({
                typeId: item.typeId,
                typeName: item.typeName,
                icon: this.navMapping[item.typeName] || 'el-icon-s-data'
            }));
            this.currentNav = this.navItems[0].typeName;
            this.getCardList(this.navItems[0].typeId);
        },

        async getCardList(typeId) {
            let { data } = await $request(
                'post',
                'mtexapi/region-service/homepage/sidebar/cards/',
                {
                    typeId
                }
            );
            data.forEach((card) => {
                card.renderType = this.renderTypeMapping[card.chartType];
            });
            this.cardList = data;
            this.allCardList = data;
        },
        handleNavChange(item) {
            this.currentNav = item.typeName;
            this.getCardList(item.typeId);
        },
        handleSearch(val) {
            this.searchKey = val;
        },
        handleCardClick(item) {
            if (item.jumpType === '弹窗') {
                this.dialogVisible = true;
                this.currentCardInfo = item;
                console.log('item', item);
            } else {
                const path = this.$route.path;
                const match = path.match(/\/mtex\/home\/<USER>\/]+)/);

                let params = {
                    gdb_appId: this.appId,
                    appId: ''
                };
                // 如果展板页面是被嵌套的，则传展板页的父页面id
                if (match && match[1] && match[1] !== this.appId) {
                    params.appId = match[1];
                }
                frameService.appOpenById(item.appId, params);
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import './indexPlugin.less';
</style>
