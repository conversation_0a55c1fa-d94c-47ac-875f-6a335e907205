<template>
    <div class="content">
        <Card title="站点信息">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { ruleValidatorWGS84, getLabelByValue } from '@/script/utils/method.js';
import { SHANDONG_CITY } from '@/script/constant/shandong';

export default {
    name: 'detail-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            formData: {
                stationName: '',
                cityId: '',
                latitudeLongitude: ''
            },
            nowFields: {
                add: ['stationName', 'cityId', 'latitudeLongitude'],
                edit: ['stationName', 'cityId', 'latitudeLongitude'],
                check: ['stationName', 'cityId', 'latitudeLongitude']
            },
            formConfig: [
                {
                    prop: 'stationName',
                    label: '公交站点名称：',
                    type: 'input',
                    placeholder: '请输入站点的现用正式名称',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'cityId',
                    label: '归属城市：',
                    type: 'el-select',
                    placeholder: '请选择归属城市',
                    options: SHANDONG_CITY(),
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY(), value)
                },
                {
                    prop: 'latitudeLongitude',
                    label: '经纬度坐标：',
                    type: 'input',
                    placeholder: '请输入站点WGS84坐标系经纬度',
                    attrs: {
                        clearable: true
                    }
                }
            ],
            rules: {
                stationName: [{ required: true, message: '请输入公交站点名称', trigger: 'blur' }],
                cityId: [{ required: true, message: '请选择归属城市', trigger: 'change' }],
                latitudeLongitude: [
                    { required: true, message: '请输入坐标', trigger: 'blur' },
                    {
                        validator: ruleValidatorWGS84,
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    computed: {
        modeText() {
            const map = new Map([
                ['add', '新增'],
                ['edit', '修改'],
                ['check', '查看']
            ]);
            return map.get(this.action);
        },
        getCityName() {
            const city = SHANDONG_CITY().find((item) => item.value === this.formData.cityId);
            if (city) {
                return city.label;
            }
            return this.formData.cityName;
        }
    },
    methods: {
        handleSubmit(form, modeText) {
            const isEdit = this.action === 'edit';
            $request(
                'post',
                `mtexapi/region-service/management/bus/station/${isEdit ? 'update' : 'add'}`,
                {
                    id: this.formData.id || undefined,
                    cityId: form.cityId,
                    cityName: getLabelByValue(SHANDONG_CITY(), form.cityId),
                    latitude: form.latitudeLongitude.split(',')[1],
                    line: '',
                    lineName: '',
                    lineStationId: 1,
                    longitude: form.latitudeLongitude.split(',')[0],
                    stationName: form.stationName
                }
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = {
                ...this.formData,
                ...this.row,
                latitudeLongitude: this.row.longitude + ',' + this.row.latitude
            };
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
