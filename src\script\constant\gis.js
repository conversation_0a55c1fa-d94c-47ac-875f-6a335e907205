export function getMayType() {
    let hostName = window.location.hostname;
    // 判断是本地、测试内网还是生产地址。
    if (
        hostName.indexOf('localhost') >= 0 ||
        hostName.indexOf('192.168') >= 0 ||
        hostName.indexOf('127.0') >= 0
    ) {
        return 'tx';
    }
    return 'default';
}

export function changeGisColor(gis) {
    // 改变底图颜色
    // 获取底图图层
    const mapName = (() => {
        if (getMayType() === 'default') {
            return '高德底图';
        }
        return '底图图层';
    })();
    // console.log(`【${mapName}】`);
    let colors = [0x09ecf6, 0x090c3e];
    gis.tileLayerList[mapName].colorChangeDate.dark.setHex(colors[0]);
    gis.tileLayerList[mapName].colorChangeDate.light.setHex(colors[1]);
    gis.gis.color = colors[1];
}

export const gistotalOptions = {
    city: {
        lng: 117.1140042,
        lat: 36.6507007,
        cityID: 370100,
        cityName: '济南市'
    },
    initialZoom: 10,
    options: {
        bloom: true,
        antialias: true,
        divLayer: true,
        mapLayer: {
            visible: true,
            type: 22,
            mapType: getMayType(),
            colorControl: true
        },
        amapMapLayer: {
            visible: true,
            colorControl: true
        },
        cameraControl: {
            moveState: true,
            wheelState: true,
            type: '2d',
            wheelLevelChange: false,
            typeButton: true,
            minZoom: 5,
            maxZoom: 17,
            cameraNearCrop: false
        },
        lineLayer: true,
        migration: true,
        areaEditLayer: true,
        lineEditLayer: true,
        rectangleSelectLayer: {
            visible: true,
            needFrameLabel: true
        }
    }
};
