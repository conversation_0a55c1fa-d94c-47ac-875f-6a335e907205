<template>
    <commonTableContent
        ref="busStopListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
        <!-- 自定义插槽 -->
        <template #lng_lat="{ row }">
            <span>{{ row.longitude }},{{ row.latitude }}</span>
        </template>
        <template #operation="{ row }">
            <el-button class="p-0" type="text" @click="del(row)">删除</el-button>
            <el-button class="p-0" type="text" @click="check(row)">重启</el-button>
            <el-button class="p-0" type="text" @click="edit(row)">编辑</el-button>
        </template>
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import { stopFormCols, stopTableCols } from '@/script/constant/cityRoadNetwork/busStopManager.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                stationName: undefined,
                cityId: ''
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return stopFormCols;
        },
        columns() {
            return stopTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                stationName: params.stationName,
                cityId: params.cityId,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/bus/station/query',
                payload
            );
            this.tableData = data.data;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            this.$confirm('确定要删除该站点吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/management/bus/station/delete', {
                        ids: [row.id]
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.busStopListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
